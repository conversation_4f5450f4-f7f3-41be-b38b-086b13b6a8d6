import 'package:flutter_test/flutter_test.dart';
import 'package:doc_transcribe/main.dart';

void main() {
  testWidgets('DocTranscribe app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const DocTranscribeApp());

    // Verify that the app starts with the home screen
    expect(find.text('DocTranscribe'), findsOneWidget);
    expect(find.text('Welcome to DocTranscribe'), findsOneWidget);
    expect(find.text('New Consultation'), findsOneWidget);
  });

  testWidgets('Navigation to patient info screen', (WidgetTester tester) async {
    await tester.pumpWidget(const DocTranscribeApp());

    // Find and tap the new consultation button
    await tester.tap(find.text('New Consultation'));
    await tester.pumpAndSettle();

    // This test would need API key configuration to pass
    // For now, it should show the API key required dialog
    expect(find.text('API Key Required'), findsOneWidget);
  });
}
