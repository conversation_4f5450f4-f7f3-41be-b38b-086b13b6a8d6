import 'dart:async';
import 'dart:typed_data';
import '../models/transcript.dart';
import '../utils/uuid_generator.dart';

class SimplifiedRivaService {
  static const String defaultEndpoint = 'https://api.nvcf.nvidia.com/v2/nvcf/pexec/functions';
  static const String functionId = 'ee8dc628-76de-4acc-8595-1836e7e857bd'; // Canary-1B ASR
  
  StreamController<TranscriptSegment>? _transcriptController;
  StreamSubscription? _audioSubscription;
  Timer? _simulationTimer;
  bool _isConnected = false;
  String? _apiKey;
  String? _language;

  Stream<TranscriptSegment> get transcriptStream => 
      _transcriptController?.stream ?? const Stream.empty();

  bool get isConnected => _isConnected;

  // Initialize Riva service
  Future<void> initialize({
    required String apiKey,
    required String language,
    String? endpoint,
  }) async {
    try {
      _apiKey = apiKey;
      _language = language;

      // Initialize transcript stream
      _transcriptController = StreamController<TranscriptSegment>.broadcast();

      _isConnected = true;
    } catch (e) {
      throw Exception('Failed to initialize Riva service: $e');
    }
  }

  // Start streaming recognition (simulated with realistic medical conversation)
  Future<void> startStreamingRecognition(Stream<Uint8List> audioStream) async {
    if (!_isConnected) {
      throw Exception('Riva service not properly initialized');
    }

    try {
      // Listen to audio stream (for demo, we'll simulate transcription)
      _audioSubscription = audioStream.listen(
        (audioData) {
          // In a real implementation, this would send audio data to NVIDIA Riva
          // For now, we'll trigger realistic transcription simulation
        },
        onError: (error) => _transcriptController?.addError(error),
        onDone: () => _finishRecognition(),
      );

      // Start realistic medical conversation simulation
      _startRealisticTranscription();
    } catch (e) {
      throw Exception('Failed to start streaming recognition: $e');
    }
  }

  // Process audio file (offline transcription)
  Future<List<TranscriptSegment>> transcribeAudioFile(
    String audioFilePath,
    String language,
  ) async {
    if (!_isConnected) {
      throw Exception('Riva service not properly initialized');
    }

    try {
      // In a real implementation, this would send the file to NVIDIA Riva
      // For demo, we'll return a realistic medical consultation transcript
      await Future.delayed(const Duration(seconds: 2));
      return _generateRealisticTranscript();
    } catch (e) {
      throw Exception('Failed to transcribe audio file: $e');
    }
  }

  // Private methods
  void _startRealisticTranscription() {
    // Generate realistic medical conversation in real-time
    final medicalConversation = [
      ("Doctor", "Good morning! Please have a seat. What brings you in today?"),
      ("Patient", "Thank you, doctor. I've been having some chest pain for the past few days."),
      ("Doctor", "I see. Can you tell me more about this chest pain? When did it start exactly?"),
      ("Patient", "It started about three days ago. It's a dull ache that comes and goes."),
      ("Doctor", "Is the pain constant, or does it happen at specific times?"),
      ("Patient", "It seems to get worse when I'm lying down or after eating."),
      ("Doctor", "Have you experienced any shortness of breath or nausea with this pain?"),
      ("Patient", "Yes, I have felt a bit short of breath, especially when climbing stairs."),
      ("Doctor", "Any family history of heart disease or similar symptoms?"),
      ("Patient", "My father had a heart attack when he was in his sixties."),
      ("Doctor", "I understand your concern. Let me examine you and then we'll discuss next steps."),
      ("Patient", "Okay, doctor. Should I be worried about this?"),
      ("Doctor", "Let's not jump to conclusions. The examination and tests will give us more information."),
      ("Patient", "What kind of tests are you thinking about?"),
      ("Doctor", "I'd like to do an EKG and some blood work to start with."),
      ("Patient", "How long will those take?"),
      ("Doctor", "The EKG is immediate, and we'll have blood results in 24-48 hours."),
      ("Patient", "And if those show something concerning?"),
      ("Doctor", "Then we might need additional imaging, but let's take this one step at a time."),
      ("Patient", "Alright, I appreciate your thoroughness, doctor."),
    ];

    int segmentIndex = 0;
    _simulationTimer = Timer.periodic(const Duration(seconds: 4), (timer) {
      if (!_isConnected || _transcriptController?.isClosed == true || segmentIndex >= medicalConversation.length) {
        timer.cancel();
        return;
      }

      final (speakerLabel, text) = medicalConversation[segmentIndex];
      final speaker = speakerLabel == "Doctor" ? SpeakerType.doctor : SpeakerType.patient;

      final segment = TranscriptSegment(
        id: UuidGenerator.generate(),
        timestamp: DateTime.now(),
        speaker: speaker,
        text: text,
        isFinal: true,
        confidence: 0.88 + (segmentIndex % 12) / 100, // Varied confidence scores
      );

      _transcriptController?.add(segment);
      segmentIndex++;
    });
  }

  void _finishRecognition() {
    // Called when streaming recognition is complete
    _simulationTimer?.cancel();
  }

  List<TranscriptSegment> _generateRealisticTranscript() {
    // Generate a complete realistic medical consultation transcript
    final segments = <TranscriptSegment>[];
    final baseTime = DateTime.now().subtract(const Duration(minutes: 10));

    final medicalConversation = [
      ("Doctor", "Good morning, please have a seat. What brings you in today?"),
      ("Patient", "Thank you, doctor. I've been having some chest pain for the past few days."),
      ("Doctor", "I see. Can you tell me more about this chest pain? When did it start?"),
      ("Patient", "It started about three days ago. It's a dull ache that comes and goes."),
      ("Doctor", "Is the pain constant, or does it happen at specific times?"),
      ("Patient", "It seems to get worse when I'm lying down or after eating."),
      ("Doctor", "Have you experienced any shortness of breath or nausea?"),
      ("Patient", "Yes, I have felt a bit short of breath, especially when climbing stairs."),
      ("Doctor", "Any family history of heart disease or similar symptoms?"),
      ("Patient", "My father had a heart attack when he was in his sixties."),
      ("Doctor", "I understand your concern. Let me examine you and then we'll discuss next steps."),
      ("Patient", "Okay, doctor. Should I be worried about this?"),
      ("Doctor", "Let's not jump to conclusions. The examination and tests will give us more information."),
      ("Patient", "What kind of tests are you thinking about?"),
      ("Doctor", "I'd like to do an EKG and some blood work to start with."),
      ("Patient", "How long will those take?"),
      ("Doctor", "The EKG is immediate, and we'll have blood results in 24-48 hours."),
      ("Patient", "And if those show something concerning?"),
      ("Doctor", "Then we might need additional imaging, but let's take this one step at a time."),
      ("Patient", "Alright, I appreciate your thoroughness, doctor."),
    ];

    for (int i = 0; i < medicalConversation.length; i++) {
      final (speakerLabel, text) = medicalConversation[i];
      final speaker = speakerLabel == "Doctor" ? SpeakerType.doctor : SpeakerType.patient;
      final timestamp = baseTime.add(Duration(seconds: i * 15 + (i % 3) * 5)); // Varied timing

      segments.add(TranscriptSegment(
        id: UuidGenerator.generate(),
        timestamp: timestamp,
        speaker: speaker,
        text: text,
        isFinal: true,
        confidence: 0.88 + (i % 12) / 100, // Varied confidence scores
      ));
    }

    return segments;
  }

  // Disconnect from Riva service
  Future<void> disconnect() async {
    try {
      _simulationTimer?.cancel();
      await _audioSubscription?.cancel();
      await _transcriptController?.close();

      _simulationTimer = null;
      _audioSubscription = null;
      _transcriptController = null;
      _isConnected = false;
    } catch (e) {
      // Log error but don't throw
      print('Error disconnecting from Riva service: $e');
    }
  }

  // Get supported languages
  static Map<String, String> getSupportedLanguages() {
    return {
      'en-US': 'English (US)',
      'es-ES': 'Spanish (Spain)',
      'hi-IN': 'Hindi (India)',
      'fr-FR': 'French (France)',
      'de-DE': 'German (Germany)',
      'it-IT': 'Italian (Italy)',
      'pt-BR': 'Portuguese (Brazil)',
      'ja-JP': 'Japanese (Japan)',
      'ko-KR': 'Korean (Korea)',
      'zh-CN': 'Chinese (Simplified)',
    };
  }

  // Validate API key format
  static bool isValidApiKey(String apiKey) {
    return apiKey.length > 20; // Simplified validation
  }
}
