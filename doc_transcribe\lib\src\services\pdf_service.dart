import 'dart:io';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:intl/intl.dart';
import '../models/consultation.dart';
import '../models/patient.dart';
import '../models/transcript.dart';
import 'storage_service.dart';

class PdfService {
  static final PdfService _instance = PdfService._internal();
  static PdfService get instance => _instance;
  PdfService._internal();

  // Generate PDF report from consultation
  Future<String> generateConsultationReport(
    Consultation consultation, {
    String? doctorName,
    String? clinicName,
    String? clinicAddress,
  }) async {
    try {
      final pdf = pw.Document();
      final patient = consultation.patient;
      final transcript = consultation.transcript;

      // Add cover page
      pdf.addPage(await _buildCoverPage(
        patient: patient,
        consultation: consultation,
        doctorName: doctorName,
        clinicName: clinicName,
      ));

      // Add transcript pages if available
      if (transcript != null && transcript.doctorPatientSegments.isNotEmpty) {
        final transcriptPages = await _buildTranscriptPages(transcript);
        for (final page in transcriptPages) {
          pdf.addPage(page);
        }
      }

      // Add doctor's notes page if available
      if (transcript?.doctorNotes != null && transcript!.doctorNotes!.isNotEmpty) {
        pdf.addPage(await _buildNotesPage(transcript.doctorNotes!));
      }

      // Generate PDF bytes
      final pdfBytes = await pdf.save();

      // Save to file
      final filePath = StorageService.instance.getPdfFilePath(
        consultation.id,
        patient.name,
      );
      
      final file = File(filePath);
      await file.writeAsBytes(pdfBytes);

      return filePath;
    } catch (e) {
      throw Exception('Failed to generate PDF report: $e');
    }
  }

  // Build cover page
  Future<pw.Page> _buildCoverPage({
    required Patient patient,
    required Consultation consultation,
    String? doctorName,
    String? clinicName,
  }) async {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(40),
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Header
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.only(bottom: 20),
              decoration: const pw.BoxDecoration(
                border: pw.Border(
                  bottom: pw.BorderSide(width: 2, color: PdfColors.blue),
                ),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                children: [
                  if (clinicName != null) ...[
                    pw.Text(
                      clinicName,
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.blue,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                  ],
                  pw.Text(
                    'Consultation Transcript Report',
                    style: pw.TextStyle(
                      fontSize: 20,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 40),

            // Patient Information
            pw.Text(
              'Patient Information',
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.blue,
              ),
            ),
            pw.SizedBox(height: 15),
            
            _buildInfoRow('Name:', patient.name),
            _buildInfoRow('Age:', '${patient.age} years'),
            _buildInfoRow('Gender:', patient.gender),
            
            if (patient.medicalRecordNumber != null)
              _buildInfoRow('Medical Record Number:', patient.medicalRecordNumber!),
            
            if (patient.reasonForVisit != null)
              _buildInfoRow('Reason for Visit:', patient.reasonForVisit!),

            pw.SizedBox(height: 30),

            // Consultation Information
            pw.Text(
              'Consultation Information',
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.blue,
              ),
            ),
            pw.SizedBox(height: 15),

            _buildInfoRow('Date:', _formatDate(consultation.createdAt)),
            _buildInfoRow('Time:', _formatTime(consultation.createdAt)),
            
            if (doctorName != null)
              _buildInfoRow('Doctor:', doctorName),
            
            if (consultation.duration != null)
              _buildInfoRow('Duration:', consultation.formattedDuration),

            pw.Spacer(),

            // Footer
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.only(top: 20),
              decoration: const pw.BoxDecoration(
                border: pw.Border(
                  top: pw.BorderSide(width: 1, color: PdfColors.grey),
                ),
              ),
              child: pw.Column(
                children: [
                  pw.Text(
                    'This report contains confidential patient information.',
                    style: const pw.TextStyle(
                      fontSize: 10,
                      color: PdfColors.grey,
                    ),
                  ),
                  pw.Text(
                    'Unauthorized dissemination is strictly prohibited.',
                    style: const pw.TextStyle(
                      fontSize: 10,
                      color: PdfColors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  // Build transcript pages
  Future<List<pw.Page>> _buildTranscriptPages(Transcript transcript) async {
    final pages = <pw.Page>[];
    final segments = transcript.doctorPatientSegments;
    const segmentsPerPage = 15; // Approximate number of segments per page

    for (int i = 0; i < segments.length; i += segmentsPerPage) {
      final pageSegments = segments.skip(i).take(segmentsPerPage).toList();
      
      pages.add(pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Page header
              _buildPageHeader('Consultation Transcript', context.pageNumber),
              
              pw.SizedBox(height: 20),

              // Transcript content
              ...pageSegments.map((segment) => _buildTranscriptSegment(segment)),

              pw.Spacer(),

              // Page footer
              _buildPageFooter(context.pageNumber),
            ],
          );
        },
      ));
    }

    return pages;
  }

  // Build doctor's notes page
  Future<pw.Page> _buildNotesPage(String notes) async {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(40),
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Page header
            _buildPageHeader('Doctor\'s Notes & Impression', context.pageNumber),
            
            pw.SizedBox(height: 20),

            // Notes content
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(15),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
              ),
              child: pw.Text(
                notes,
                style: const pw.TextStyle(fontSize: 12),
              ),
            ),

            pw.Spacer(),

            // Page footer
            _buildPageFooter(context.pageNumber),
          ],
        );
      },
    );
  }

  // Helper widgets
  pw.Widget _buildInfoRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 8),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.SizedBox(
            width: 150,
            child: pw.Text(
              label,
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Expanded(
            child: pw.Text(value),
          ),
        ],
      ),
    );
  }

  pw.Widget _buildPageHeader(String title, int pageNumber) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.only(bottom: 10),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(width: 1, color: PdfColors.grey),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.Text(
            'Page $pageNumber',
            style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey),
          ),
        ],
      ),
    );
  }

  pw.Widget _buildTranscriptSegment(TranscriptSegment segment) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 12),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Timestamp
          pw.SizedBox(
            width: 80,
            child: pw.Text(
              segment.formattedTimestamp,
              style: const pw.TextStyle(
                fontSize: 10,
                color: PdfColors.grey,
              ),
            ),
          ),
          
          // Speaker and text
          pw.Expanded(
            child: pw.RichText(
              text: pw.TextSpan(
                children: [
                  pw.TextSpan(
                    text: '${segment.speakerLabel}: ',
                    style: pw.TextStyle(
                      fontSize: 12,
                      fontWeight: pw.FontWeight.bold,
                      color: segment.speaker == SpeakerType.doctor 
                          ? PdfColors.blue 
                          : PdfColors.green,
                    ),
                  ),
                  pw.TextSpan(
                    text: segment.text,
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  pw.Widget _buildPageFooter(int pageNumber) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.only(top: 10),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(width: 1, color: PdfColors.grey300),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'DocTranscribe - Confidential Medical Record',
            style: const pw.TextStyle(fontSize: 8, color: PdfColors.grey),
          ),
          pw.Text(
            'Page $pageNumber',
            style: const pw.TextStyle(fontSize: 8, color: PdfColors.grey),
          ),
        ],
      ),
    );
  }

  // Utility methods
  String _formatDate(DateTime dateTime) {
    return DateFormat('MMMM dd, yyyy').format(dateTime);
  }

  String _formatTime(DateTime dateTime) {
    return DateFormat('hh:mm a').format(dateTime);
  }

  // Preview PDF (for testing)
  Future<void> previewPdf(String filePath) async {
    final file = File(filePath);
    if (await file.exists()) {
      final bytes = await file.readAsBytes();
      await Printing.layoutPdf(onLayout: (format) => bytes);
    }
  }
}
