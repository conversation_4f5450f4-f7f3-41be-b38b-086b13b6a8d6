import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/consultation.dart';
import '../models/patient_info.dart';
import '../services/api_service.dart';
import '../services/audio_service.dart';
import '../services/storage_service.dart';
import '../services/pdf_service.dart';
import '../utils/constants.dart';

class ConsultationProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  final AudioService _audioService = AudioService.instance;
  final StorageService _storageService = StorageService.instance;
  final PdfService _pdfService = PdfService.instance;
  
  Consultation? _currentConsultation;
  List<Consultation> _consultationHistory = [];
  bool _isLoading = false;
  String? _error;
  Timer? _recordingTimer;
  Duration _recordingDuration = Duration.zero;

  // Getters
  Consultation? get currentConsultation => _currentConsultation;
  List<Consultation> get consultationHistory => _consultationHistory;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Duration get recordingDuration => _recordingDuration;
  bool get hasActiveConsultation => _currentConsultation != null;
  bool get isRecording => _currentConsultation?.isRecording ?? false;

  // Initialize a new consultation
  Future<void> startNewConsultation(PatientInfo patientInfo, String doctorName) async {
    _setLoading(true);
    _clearError();

    try {
      // Initialize audio service if needed
      if (!_audioService.isInitialized) {
        await _audioService.initialize();
      }

      // Start consultation on backend
      final consultationId = await _apiService.startConsultation(patientInfo, doctorName);

      // Create local consultation object
      _currentConsultation = Consultation(
        id: consultationId,
        patient: patientInfo,
        doctorName: doctorName,
        status: ConsultationStatus.draft,
      );

      notifyListeners();
    } catch (e) {
      _setError('Failed to start consultation: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Start recording audio
  Future<void> startRecording() async {
    if (_currentConsultation == null) {
      _setError('No active consultation');
      return;
    }

    _setLoading(true);
    _clearError();

    try {
      await _audioService.startRecording();
      
      _currentConsultation = _currentConsultation!.copyWith(
        status: ConsultationStatus.recording,
      );

      _startRecordingTimer();
      notifyListeners();
    } catch (e) {
      _setError('Failed to start recording: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Stop recording and process transcript
  Future<void> stopRecording() async {
    if (_currentConsultation == null || !_currentConsultation!.isRecording) {
      _setError('Not currently recording');
      return;
    }

    _setLoading(true);
    _clearError();

    try {
      // Stop audio recording
      final audioFilePath = await _audioService.stopRecording();
      _stopRecordingTimer();

      // Update consultation status
      _currentConsultation = _currentConsultation!.copyWith(
        status: ConsultationStatus.processing,
        audioFilePath: audioFilePath,
        duration: _recordingDuration,
      );
      notifyListeners();

      // Finish consultation and get cleaned transcript
      final transcript = await _apiService.finishConsultation();

      // Update consultation with transcript
      _currentConsultation = _currentConsultation!.copyWith(
        status: ConsultationStatus.completed,
        transcript: transcript,
        completedAt: DateTime.now(),
      );

      notifyListeners();
    } catch (e) {
      _currentConsultation = _currentConsultation!.copyWith(
        status: ConsultationStatus.error,
        errorMessage: e.toString(),
      );
      _setError('Failed to process recording: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Add doctor notes to consultation
  void addDoctorNotes(String notes) {
    if (_currentConsultation != null) {
      _currentConsultation = _currentConsultation!.copyWith(doctorNotes: notes);
      notifyListeners();
    }
  }

  // Save consultation to history
  Future<void> saveConsultation() async {
    if (_currentConsultation == null) return;

    try {
      await _storageService.saveConsultation(_currentConsultation!);
      await loadConsultationHistory(); // Refresh the list
    } catch (e) {
      _setError('Failed to save consultation: $e');
    }
  }

  // Load consultation history
  Future<void> loadConsultationHistory() async {
    _setLoading(true);
    _clearError();

    try {
      _consultationHistory = await _storageService.getConsultations();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load consultation history: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Generate PDF for current consultation
  Future<String?> generatePdf({String? additionalNotes}) async {
    if (_currentConsultation == null) return null;

    _setLoading(true);
    _clearError();

    try {
      // Add notes to consultation if provided
      if (additionalNotes != null && additionalNotes.isNotEmpty) {
        _currentConsultation = _currentConsultation!.copyWith(
          doctorNotes: additionalNotes,
        );
      }

      // Generate PDF
      final pdfPath = await _pdfService.generateConsultationReport(
        _currentConsultation!,
        additionalNotes: additionalNotes,
      );

      // Update consultation with PDF path
      _currentConsultation = _currentConsultation!.copyWith(
        pdfFilePath: pdfPath,
      );

      // Save updated consultation
      await _storageService.saveConsultation(_currentConsultation!);

      notifyListeners();
      return pdfPath;
    } catch (e) {
      _setError('Failed to generate PDF: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // Share PDF
  Future<void> sharePdf(String pdfPath) async {
    try {
      await _pdfService.sharePdf(pdfPath);
    } catch (e) {
      _setError('Failed to share PDF: $e');
    }
  }

  // Clear current consultation
  void clearCurrentConsultation() {
    _currentConsultation = null;
    _recordingDuration = Duration.zero;
    _stopRecordingTimer();
    notifyListeners();
  }

  // Delete consultation from history
  Future<void> deleteConsultation(String consultationId) async {
    try {
      await _storageService.deleteConsultation(consultationId);
      await loadConsultationHistory(); // Refresh the list
    } catch (e) {
      _setError('Failed to delete consultation: $e');
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  void _startRecordingTimer() {
    _recordingDuration = Duration.zero;
    _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _recordingDuration = Duration(seconds: timer.tick);
      notifyListeners();
    });
  }

  void _stopRecordingTimer() {
    _recordingTimer?.cancel();
    _recordingTimer = null;
  }

  @override
  void dispose() {
    _stopRecordingTimer();
    super.dispose();
  }
}
