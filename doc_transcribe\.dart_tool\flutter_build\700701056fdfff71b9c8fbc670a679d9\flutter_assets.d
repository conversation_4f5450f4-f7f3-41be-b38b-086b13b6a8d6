 C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_sound/assets/js/async_processor.js C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_sound/assets/js/tau_web.js C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_sound_web/howler/howler.js C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_sound_web/src/flutter_sound.js C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_sound_web/src/flutter_sound_player.js C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_sound_web/src/flutter_sound_recorder.js C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/flutter_sound_web/src/flutter_sound_stream_processor.js C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json C:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z:  C:\\python_programs\\DocScribe\\doc_transcribe\\pubspec.yaml C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound-9.28.0\\assets\\js\\async_processor.js C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound-9.28.0\\assets\\js\\tau_web.js C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound_web-9.28.0\\howler\\howler.js C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound_web-9.28.0\\src\\flutter_sound.js C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound_web-9.28.0\\src\\flutter_sound_player.js C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound_web-9.28.0\\src\\flutter_sound_recorder.js C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound_web-9.28.0\\src\\flutter_sound_stream_processor.js E:\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf E:\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\archive-3.6.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\args-2.7.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audio_waveforms-1.3.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\barcode-2.2.9\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\bidi-2.0.13\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-3.0.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound-9.28.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound_platform_interface-9.28.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_sound_web-9.28.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_identity_services_web-0.3.3+1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\googleapis_auth-1.6.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\grpc-3.2.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http2-2.3.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image-4.3.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.19.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-3.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logger-2.5.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.16+1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.15.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-1.0.6\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\nested-1.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_parsing-1.1.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.15\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pdf-3.11.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pdf_widget_wrapper-1.0.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler-11.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_android-12.1.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_apple-9.4.7\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_windows-0.2.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\printing-5.14.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\protobuf-3.1.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\qr-3.0.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-7.2.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus_platform_interface-3.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.11.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\synchronized-3.3.0+3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_web-2.3.3\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-14.2.5\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.10.1\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\LICENSE E:\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE E:\\flutter\\packages\\flutter\\LICENSE C:\\python_programs\\DocScribe\\doc_transcribe\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD217613697