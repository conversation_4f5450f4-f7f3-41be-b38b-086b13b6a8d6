import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../utils/constants.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  static AudioService get instance => _instance;
  AudioService._internal();

  FlutterSoundRecorder? _recorder;
  StreamSubscription? _recordingDataSubscription;
  StreamController<Uint8List>? _audioStreamController;
  StreamController<double>? _audioLevelController;
  
  String? _currentRecordingPath;
  bool _isInitialized = false;
  bool _isRecording = false;
  bool _isPaused = false;
  double _currentLevel = 0.0;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isRecording => _isRecording;
  bool get isPaused => _isPaused;
  double get currentLevel => _currentLevel;
  String? get currentRecordingPath => _currentRecordingPath;

  // Streams
  Stream<Uint8List> get audioStream => _audioStreamController?.stream ?? const Stream.empty();
  Stream<double> get audioLevelStream => _audioLevelController?.stream ?? const Stream.empty();

  // Initialize the audio service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Request microphone permission
      final permission = await Permission.microphone.request();
      if (permission != PermissionStatus.granted) {
        throw AudioException('Microphone permission denied');
      }

      // Initialize recorder
      _recorder = FlutterSoundRecorder();
      await _recorder!.openRecorder();

      // Initialize stream controllers
      _audioStreamController = StreamController<Uint8List>.broadcast();
      _audioLevelController = StreamController<double>.broadcast();

      _isInitialized = true;
    } catch (e) {
      throw AudioException('Failed to initialize audio service: $e');
    }
  }

  // Start recording audio
  Future<void> startRecording() async {
    if (!_isInitialized) {
      throw AudioException('Audio service not initialized');
    }

    if (_isRecording) {
      throw AudioException('Already recording');
    }

    try {
      // Create recording file path
      final directory = await getApplicationDocumentsDirectory();
      final audioDir = Directory(path.join(directory.path, 'DocScribe', 'audio'));
      await audioDir.create(recursive: true);
      
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      _currentRecordingPath = path.join(audioDir.path, 'recording_$timestamp.wav');

      // Start recording with optimal settings for NVIDIA Riva
      await _recorder!.startRecorder(
        toFile: _currentRecordingPath,
        codec: Codec.pcm16WAV,
        sampleRate: AudioConstants.sampleRate,
        numChannels: AudioConstants.numChannels,
        bitRate: AudioConstants.sampleRate * AudioConstants.bitDepth,
      );

      // Start streaming audio data
      _startAudioStreaming();
      
      _isRecording = true;
      _isPaused = false;
    } catch (e) {
      throw AudioException('Failed to start recording: $e');
    }
  }

  // Stop recording
  Future<String?> stopRecording() async {
    if (!_isRecording) {
      throw AudioException('Not currently recording');
    }

    try {
      await _recorder!.stopRecorder();
      await _recordingDataSubscription?.cancel();
      
      _isRecording = false;
      _isPaused = false;
      
      final recordingPath = _currentRecordingPath;
      _currentRecordingPath = null;
      
      return recordingPath;
    } catch (e) {
      throw AudioException('Failed to stop recording: $e');
    }
  }

  // Pause recording
  Future<void> pauseRecording() async {
    if (!_isRecording || _isPaused) {
      throw AudioException('Cannot pause recording');
    }

    try {
      await _recorder!.pauseRecorder();
      _isPaused = true;
    } catch (e) {
      throw AudioException('Failed to pause recording: $e');
    }
  }

  // Resume recording
  Future<void> resumeRecording() async {
    if (!_isRecording || !_isPaused) {
      throw AudioException('Cannot resume recording');
    }

    try {
      await _recorder!.resumeRecorder();
      _isPaused = false;
    } catch (e) {
      throw AudioException('Failed to resume recording: $e');
    }
  }

  // Start streaming audio data for real-time processing
  void _startAudioStreaming() {
    _recordingDataSubscription = _recorder!.onProgress!.listen((event) {
      // Update audio level
      _currentLevel = _normalizeAudioLevel(event.decibels ?? -60.0);
      _audioLevelController?.add(_currentLevel);
    });

    // Start periodic audio chunk streaming
    _startChunkStreaming();
  }

  // Start streaming audio chunks for real-time transcription
  void _startChunkStreaming() {
    Timer.periodic(const Duration(milliseconds: AudioConstants.chunkDurationMs), (timer) {
      if (!_isRecording || _isPaused) {
        timer.cancel();
        return;
      }

      _captureAudioChunk();
    });
  }

  // Capture audio chunk for streaming
  Future<void> _captureAudioChunk() async {
    try {
      if (_currentRecordingPath == null || !_isRecording) return;

      // Read current audio file and extract latest chunk
      final file = File(_currentRecordingPath!);
      if (!await file.exists()) return;

      final audioBytes = await file.readAsBytes();

      // Calculate chunk size (100ms of 16kHz 16-bit mono audio)
      final chunkSize = (AudioConstants.sampleRate *
                       AudioConstants.chunkDurationMs / 1000 * 2).round();

      // Extract latest chunk if file is large enough
      if (audioBytes.length >= chunkSize) {
        final startOffset = (audioBytes.length - chunkSize).clamp(0, audioBytes.length);
        final chunk = audioBytes.sublist(startOffset);

        if (_audioStreamController != null && !_audioStreamController!.isClosed) {
          _audioStreamController!.add(Uint8List.fromList(chunk));
        }
      }
    } catch (e) {
      print('Error capturing audio chunk: $e');
    }
  }

  // Normalize audio level from decibels to percentage
  double _normalizeAudioLevel(double decibels) {
    // Convert decibels to percentage (0-100)
    // Typical range: -60dB (silence) to 0dB (max)
    const minDb = -60.0;
    const maxDb = 0.0;

    final normalizedDb = (decibels - minDb) / (maxDb - minDb);
    return (normalizedDb * 100).clamp(0.0, 100.0);
  }

  // Get audio file as bytes
  Future<Uint8List> getAudioFileBytes(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw AudioException('Audio file not found: $filePath');
      }
      return await file.readAsBytes();
    } catch (e) {
      throw AudioException('Failed to read audio file: $e');
    }
  }

  // Delete audio file
  Future<void> deleteAudioFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      throw AudioException('Failed to delete audio file: $e');
    }
  }

  // Dispose resources
  Future<void> dispose() async {
    try {
      if (_isRecording) {
        await stopRecording();
      }
      
      await _recordingDataSubscription?.cancel();
      await _audioStreamController?.close();
      await _audioLevelController?.close();
      await _recorder?.closeRecorder();
      
      _isInitialized = false;
    } catch (e) {
      // Log error but don't throw during disposal
      print('Error disposing audio service: $e');
    }
  }
}

class AudioException implements Exception {
  final String message;
  AudioException(this.message);

  @override
  String toString() => 'AudioException: $message';
}
