#Tue Jun 03 07:51:31 IST 2025
base.0=C\:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=C\:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\10\\classes.dex
base.10=C\:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\6\\classes.dex
base.11=C\:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
base.2=C\:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\12\\classes.dex
base.3=C\:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\15\\classes.dex
base.4=C\:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\1\\classes.dex
base.5=C\:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\4\\classes.dex
base.6=C\:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\8\\classes.dex
base.7=C\:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\9\\classes.dex
base.8=C\:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.9=C\:\\python_programs\\DocScribe\\doc_transcribe\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\1\\classes.dex
path.0=classes.dex
path.1=10/classes.dex
path.10=6/classes.dex
path.11=classes2.dex
path.2=12/classes.dex
path.3=15/classes.dex
path.4=1/classes.dex
path.5=4/classes.dex
path.6=8/classes.dex
path.7=9/classes.dex
path.8=0/classes.dex
path.9=1/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.10=classes11.dex
renamed.11=classes12.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
renamed.9=classes10.dex
