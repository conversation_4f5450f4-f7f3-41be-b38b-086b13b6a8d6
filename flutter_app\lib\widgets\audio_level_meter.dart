import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/constants.dart';

class AudioLevelMeter extends StatefulWidget {
  final double level;
  final double maxLevel;
  final Color activeColor;
  final Color inactiveColor;

  const AudioLevelMeter({
    super.key,
    this.level = 0.0,
    this.maxLevel = 100.0,
    this.activeColor = AppColors.success,
    this.inactiveColor = AppColors.background,
  });

  @override
  State<AudioLevelMeter> createState() => _AudioLevelMeterState();
}

class _AudioLevelMeterState extends State<AudioLevelMeter>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  double _currentLevel = 0.0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(AudioLevelMeter oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.level != oldWidget.level) {
      _updateLevel(widget.level);
    }
  }

  void _updateLevel(double newLevel) {
    setState(() {
      _currentLevel = math.min(newLevel, widget.maxLevel);
    });
    
    if (newLevel > 0) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 80,
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppSizes.borderRadiusLarge),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Audio Level',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              Text(
                '${_currentLevel.toInt()}%',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSizes.paddingSmall),
          
          Expanded(
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return CustomPaint(
                  size: const Size(double.infinity, 20),
                  painter: AudioLevelPainter(
                    level: _currentLevel,
                    maxLevel: widget.maxLevel,
                    activeColor: widget.activeColor,
                    inactiveColor: widget.inactiveColor,
                    animationValue: _animation.value,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class AudioLevelPainter extends CustomPainter {
  final double level;
  final double maxLevel;
  final Color activeColor;
  final Color inactiveColor;
  final double animationValue;

  AudioLevelPainter({
    required this.level,
    required this.maxLevel,
    required this.activeColor,
    required this.inactiveColor,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    const barCount = 20;
    final barWidth = (size.width - (barCount - 1) * 2) / barCount;
    final activeBarCount = ((level / maxLevel) * barCount).round();

    for (int i = 0; i < barCount; i++) {
      final x = i * (barWidth + 2);
      final isActive = i < activeBarCount;
      
      // Calculate bar height based on position (center is tallest)
      final centerDistance = (i - barCount / 2).abs();
      final heightMultiplier = 1.0 - (centerDistance / (barCount / 2)) * 0.5;
      final barHeight = size.height * heightMultiplier;
      
      final y = (size.height - barHeight) / 2;

      paint.color = isActive 
          ? Color.lerp(inactiveColor, activeColor, animationValue)!
          : inactiveColor;

      final rect = RRect.fromRectAndRadius(
        Rect.fromLTWH(x, y, barWidth, barHeight),
        const Radius.circular(2),
      );

      canvas.drawRRect(rect, paint);
    }
  }

  @override
  bool shouldRepaint(AudioLevelPainter oldDelegate) {
    return oldDelegate.level != level ||
           oldDelegate.animationValue != animationValue;
  }
}
