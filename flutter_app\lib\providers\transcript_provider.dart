import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import '../models/transcript_segment.dart';
import '../services/api_service.dart';
import '../services/audio_service.dart';
import '../utils/constants.dart';

class TranscriptProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  final AudioService _audioService = AudioService.instance;
  
  final List<TranscriptSegment> _liveTranscript = [];
  List<TranscriptSegment> _finalTranscript = [];
  String _partialTranscript = '';
  bool _isTranscribing = false;
  String? _error;
  StreamSubscription? _audioStreamSubscription;

  // Getters
  List<TranscriptSegment> get liveTranscript => _liveTranscript;
  List<TranscriptSegment> get finalTranscript => _finalTranscript;
  String get partialTranscript => _partialTranscript;
  bool get isTranscribing => _isTranscribing;
  String? get error => _error;
  bool get hasTranscript => _finalTranscript.isNotEmpty;

  // Statistics
  int get totalWords => _finalTranscript.fold(0, (sum, segment) => sum + segment.text.split(' ').length);
  int get doctorWords => _finalTranscript
      .where((s) => s.speaker == SpeakerType.doctor)
      .fold(0, (sum, segment) => sum + segment.text.split(' ').length);
  int get patientWords => _finalTranscript
      .where((s) => s.speaker == SpeakerType.patient)
      .fold(0, (sum, segment) => sum + segment.text.split(' ').length);

  // Start live transcription
  Future<void> startLiveTranscription() async {
    if (_isTranscribing) return;

    _clearError();
    _isTranscribing = true;
    _liveTranscript.clear();
    _partialTranscript = '';
    notifyListeners();

    try {
      // Subscribe to audio stream
      _audioStreamSubscription = _audioService.audioStream.listen(
        _processAudioChunk,
        onError: (error) {
          _setError('Audio stream error: $error');
        },
      );
    } catch (e) {
      _setError('Failed to start transcription: $e');
      _isTranscribing = false;
      notifyListeners();
    }
  }

  // Stop live transcription
  Future<void> stopLiveTranscription() async {
    if (!_isTranscribing) return;

    try {
      await _audioStreamSubscription?.cancel();
      _audioStreamSubscription = null;
      _isTranscribing = false;
      _partialTranscript = '';
      notifyListeners();
    } catch (e) {
      _setError('Failed to stop transcription: $e');
    }
  }

  // Set final transcript (from backend processing)
  void setFinalTranscript(List<TranscriptSegment> transcript) {
    _finalTranscript = transcript;
    _liveTranscript.clear();
    _partialTranscript = '';
    notifyListeners();
  }

  // Update transcript segment
  void updateTranscriptSegment(int index, TranscriptSegment updatedSegment) {
    if (index >= 0 && index < _finalTranscript.length) {
      _finalTranscript[index] = updatedSegment;
      notifyListeners();
    }
  }

  // Add transcript segment
  void addTranscriptSegment(TranscriptSegment segment) {
    _finalTranscript.add(segment);
    notifyListeners();
  }

  // Remove transcript segment
  void removeTranscriptSegment(int index) {
    if (index >= 0 && index < _finalTranscript.length) {
      _finalTranscript.removeAt(index);
      notifyListeners();
    }
  }

  // Filter transcript by speaker
  List<TranscriptSegment> getTranscriptBySpeaker(SpeakerType speaker) {
    return _finalTranscript.where((segment) => segment.speaker == speaker).toList();
  }

  // Get transcript as formatted text
  String getFormattedTranscript() {
    final buffer = StringBuffer();
    for (final segment in _finalTranscript) {
      buffer.writeln('[${segment.formattedTimestamp}] ${segment.speakerLabel}: ${segment.text}');
    }
    return buffer.toString();
  }

  // Search transcript
  List<TranscriptSegment> searchTranscript(String query) {
    if (query.isEmpty) return _finalTranscript;
    
    final lowerQuery = query.toLowerCase();
    return _finalTranscript
        .where((segment) => segment.text.toLowerCase().contains(lowerQuery))
        .toList();
  }

  // Clear all transcript data
  void clearTranscript() {
    _liveTranscript.clear();
    _finalTranscript.clear();
    _partialTranscript = '';
    _clearError();
    notifyListeners();
  }

  // Process audio chunk for live transcription
  Future<void> _processAudioChunk(Uint8List audioData) async {
    if (!_isTranscribing) return;

    try {
      final response = await _apiService.sendAudioChunk(audioData);
      
      // Update partial transcript
      if (response.partialTranscript != null) {
        _partialTranscript = response.partialTranscript!;
      }

      // Add final segments to live transcript
      for (final segment in response.finalSegments) {
        _liveTranscript.add(segment);
      }

      notifyListeners();
    } catch (e) {
      _setError('Transcription error: $e');
    }
  }

  // Private helper methods
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  @override
  void dispose() {
    _audioStreamSubscription?.cancel();
    super.dispose();
  }
}
