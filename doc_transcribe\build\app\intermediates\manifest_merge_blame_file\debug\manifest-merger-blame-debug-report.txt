1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.doc_transcribe"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
8-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="35" />
9-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:4:5-67
12-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:4:22-64
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:5:5-71
13-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:5:22-68
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:6:5-81
14-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:6:22-78
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:7:5-80
15-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:7:22-77
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:8:5-68
16-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:8:22-65
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:9:5-77
17-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:9:22-74
18    <uses-permission android:name="android.permission.BLUETOOTH" />
18-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:7:5-68
18-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:7:22-65
19    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
19-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:9:5-80
19-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:9:22-77
20    <uses-permission android:name="Manifest.permission.CAPTURE_AUDIO_OUTPUT" />
20-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:11:5-80
20-->[com.github.canardoux:flutter_sound_core:9.28.0] C:\Users\<USER>\.gradle\caches\transforms-3\f05ac9876d59d777521a6215486de729\transformed\jetified-flutter_sound_core-9.28.0\AndroidManifest.xml:11:22-77
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->[com.google.android.exoplayer:exoplayer-core:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\c14396579847727929a6847b59e764b2\transformed\jetified-exoplayer-core-2.17.1\AndroidManifest.xml:24:5-79
21-->[com.google.android.exoplayer:exoplayer-core:2.17.1] C:\Users\<USER>\.gradle\caches\transforms-3\c14396579847727929a6847b59e764b2\transformed\jetified-exoplayer-core-2.17.1\AndroidManifest.xml:24:22-76
22
23    <permission
23-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
24        android:name="com.example.doc_transcribe.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
25        android:protectionLevel="signature" />
25-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
26
27    <uses-permission android:name="com.example.doc_transcribe.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
28
29    <application
30        android:name="android.app.Application"
30-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:13:9-42
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
32        android:debuggable="true"
33        android:extractNativeLibs="false"
34        android:icon="@mipmap/ic_launcher"
34-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:14:9-43
35        android:label="DocTranscribe"
35-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:12:9-38
36        android:requestLegacyExternalStorage="true" >
36-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:15:9-52
37        <activity
37-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:17:9-39:20
38            android:name="com.example.doc_transcribe.MainActivity"
38-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:18:13-41
39            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
39-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:22:13-163
40            android:exported="true"
40-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:19:13-36
41            android:hardwareAccelerated="true"
41-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:23:13-47
42            android:launchMode="singleTop"
42-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:20:13-43
43            android:theme="@style/LaunchTheme"
43-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:21:13-47
44            android:windowSoftInputMode="adjustResize" >
44-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:24:13-55
45
46            <!--
47                 Specifies an Android theme to apply to this Activity as soon as
48                 the Android process has started. This theme is visible to the user
49                 while the Flutter UI initializes. After that, this theme continues
50                 to determine the Window background behind the Flutter UI.
51            -->
52            <meta-data
52-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:30:13-33:17
53                android:name="io.flutter.embedding.android.NormalTheme"
53-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:31:15-70
54                android:resource="@style/NormalTheme" />
54-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:32:15-52
55
56            <intent-filter android:autoVerify="true" >
56-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:35:13-38:29
56-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:35:28-53
57                <action android:name="android.intent.action.MAIN" />
57-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:36:17-68
57-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:36:25-66
58
59                <category android:name="android.intent.category.LAUNCHER" />
59-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:37:17-76
59-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:37:27-74
60            </intent-filter>
61        </activity>
62
63        <!--
64             Don't delete the meta-data below.
65             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
66        -->
67        <meta-data
67-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:43:9-45:33
68            android:name="flutterEmbedding"
68-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:44:13-44
69            android:value="2" />
69-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:45:13-30
70
71        <!-- File provider for sharing files -->
72        <provider
73            android:name="androidx.core.content.FileProvider"
73-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:49:13-62
74            android:authorities="com.example.doc_transcribe.fileprovider"
74-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:50:13-64
75            android:exported="false"
75-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:51:13-37
76            android:grantUriPermissions="true" >
76-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:52:13-47
77            <meta-data
77-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:53:13-55:54
78                android:name="android.support.FILE_PROVIDER_PATHS"
78-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:54:17-67
79                android:resource="@xml/file_paths" />
79-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:55:17-51
80        </provider>
81        <provider
81-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-18:20
82            android:name="net.nfet.flutter.printing.PrintFileProvider"
82-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
83            android:authorities="com.example.doc_transcribe.flutter.printing"
83-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-68
84            android:exported="false"
84-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-37
85            android:grantUriPermissions="true" >
85-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-47
86            <meta-data
86-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:53:13-55:54
87                android:name="android.support.FILE_PROVIDER_PATHS"
87-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:54:17-67
88                android:resource="@xml/flutter_printing_file_paths" />
88-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:55:17-51
89        </provider>
90        <!--
91           Declares a provider which allows us to store files to share in
92           '.../caches/share_plus' and grant the receiving action access
93        -->
94        <provider
94-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:15:9-23:20
95            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
95-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-77
96            android:authorities="com.example.doc_transcribe.flutter.share_provider"
96-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-74
97            android:exported="false"
97-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-37
98            android:grantUriPermissions="true" >
98-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-47
99            <meta-data
99-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:53:13-55:54
100                android:name="android.support.FILE_PROVIDER_PATHS"
100-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:54:17-67
101                android:resource="@xml/flutter_share_file_paths" />
101-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:55:17-51
102        </provider>
103        <!--
104           This manifest declared broadcast receiver allows us to use an explicit
105           Intent when creating a PendingItent to be informed of the user's choice
106        -->
107        <receiver
107-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:28:9-34:20
108            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
108-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-82
109            android:exported="false" >
109-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-37
110            <intent-filter>
110-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:31:13-33:29
111                <action android:name="EXTRA_CHOSEN_COMPONENT" />
111-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:32:17-65
111-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:32:25-62
112            </intent-filter>
113        </receiver>
114
115        <uses-library
115-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
116            android:name="androidx.window.extensions"
116-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
117            android:required="false" />
117-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
118        <uses-library
118-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
119            android:name="androidx.window.sidecar"
119-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
120            android:required="false" />
120-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
121
122        <provider
122-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
123            android:name="androidx.startup.InitializationProvider"
123-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
124            android:authorities="com.example.doc_transcribe.androidx-startup"
124-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
125            android:exported="false" >
125-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
126            <meta-data
126-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
128                android:value="androidx.startup" />
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
131                android:value="androidx.startup" />
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
132        </provider>
133
134        <receiver
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
135            android:name="androidx.profileinstaller.ProfileInstallReceiver"
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
136            android:directBootAware="false"
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
137            android:enabled="true"
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
138            android:exported="true"
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
139            android:permission="android.permission.DUMP" >
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
140            <intent-filter>
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
141                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
142            </intent-filter>
143            <intent-filter>
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
144                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
145            </intent-filter>
146            <intent-filter>
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
147                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
148            </intent-filter>
149            <intent-filter>
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
150                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
151            </intent-filter>
152        </receiver>
153    </application>
154
155</manifest>
