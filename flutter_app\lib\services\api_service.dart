import 'dart:typed_data';
import 'package:dio/dio.dart';
import '../models/consultation.dart';
import '../models/patient_info.dart';
import '../models/transcript_segment.dart';
import '../utils/constants.dart';

class ApiService {
  late final Dio _dio;
  String? _currentConsultationId;

  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConstants.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        ApiConstants.contentType: 'application/json',
      },
    ));

    // Add interceptors for logging and error handling
    _dio.interceptors.add(LogInterceptor(
      requestBody: false, // Don't log audio data
      responseBody: true,
    ));
  }

  // Start a new consultation session
  Future<String> startConsultation(PatientInfo patientInfo, String doctorName) async {
    try {
      final response = await _dio.post(
        ApiConstants.startConsultation,
        data: {
          'patient_name': patientInfo.name,
          'age': patientInfo.age,
          'gender': patientInfo.gender,
          'patient_id': patientInfo.patientId,
          'visit_reason': patientInfo.reasonForVisit,
          'doctor_name': doctorName,
          'language_code': 'en-US',
        },
      );

      _currentConsultationId = response.data['consultation_id'];
      return _currentConsultationId!;
    } catch (e) {
      throw ApiException('Failed to start consultation: $e');
    }
  }

  // Send audio chunk for real-time transcription
  Future<AudioChunkResponse> sendAudioChunk(Uint8List audioData) async {
    if (_currentConsultationId == null) {
      throw ApiException('No active consultation');
    }

    try {
      final formData = FormData.fromMap({
        'audio': MultipartFile.fromBytes(audioData, filename: 'chunk.wav'),
      });

      final response = await _dio.post(
        ApiConstants.audioChunk,
        data: formData,
        options: Options(
          headers: {
            ApiConstants.consultationId: _currentConsultationId,
          },
        ),
      );

      return AudioChunkResponse.fromJson(response.data);
    } catch (e) {
      throw ApiException('Failed to send audio chunk: $e');
    }
  }

  // Finish consultation and get cleaned transcript
  Future<List<TranscriptSegment>> finishConsultation() async {
    if (_currentConsultationId == null) {
      throw ApiException('No active consultation');
    }

    try {
      final response = await _dio.post(
        ApiConstants.finishConsultation,
        options: Options(
          headers: {
            ApiConstants.consultationId: _currentConsultationId,
          },
        ),
      );

      final cleanTranscript = response.data['clean_transcript'] as List;
      return cleanTranscript
          .map((segment) => TranscriptSegment.fromJson(segment))
          .toList();
    } catch (e) {
      throw ApiException('Failed to finish consultation: $e');
    } finally {
      _currentConsultationId = null;
    }
  }

  // Generate PDF (optional - if done server-side)
  Future<String> generatePdf(String doctorNotes) async {
    if (_currentConsultationId == null) {
      throw ApiException('No active consultation');
    }

    try {
      final response = await _dio.post(
        ApiConstants.generatePdf,
        data: {'doctor_notes': doctorNotes},
        options: Options(
          headers: {
            ApiConstants.consultationId: _currentConsultationId,
          },
        ),
      );

      return response.data['pdf_url'];
    } catch (e) {
      throw ApiException('Failed to generate PDF: $e');
    }
  }

  // Get consultation history
  Future<List<Consultation>> getConsultations({String? doctorId}) async {
    try {
      final response = await _dio.get(
        ApiConstants.consultations,
        queryParameters: doctorId != null ? {'doctor_id': doctorId} : null,
      );

      final consultations = response.data as List;
      return consultations
          .map((consultation) => Consultation.fromJson(consultation))
          .toList();
    } catch (e) {
      throw ApiException('Failed to get consultations: $e');
    }
  }
}

class AudioChunkResponse {
  final String status;
  final String? partialTranscript;
  final List<TranscriptSegment> finalSegments;
  final String? transcriptId;

  AudioChunkResponse({
    required this.status,
    this.partialTranscript,
    this.finalSegments = const [],
    this.transcriptId,
  });

  factory AudioChunkResponse.fromJson(Map<String, dynamic> json) {
    return AudioChunkResponse(
      status: json['status'],
      partialTranscript: json['partial_transcript'],
      finalSegments: (json['final_segments'] as List?)
              ?.map((segment) => TranscriptSegment.fromJson(segment))
              .toList() ??
          [],
      transcriptId: json['transcript_id'],
    );
  }
}

class ApiException implements Exception {
  final String message;
  ApiException(this.message);

  @override
  String toString() => 'ApiException: $message';
}
