import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_state_provider.dart';
import '../services/storage_service.dart';
import '../widgets/app_scaffold.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _doctorNameController = TextEditingController();
  final _apiKeyController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _doctorNameController.dispose();
    _apiKeyController.dispose();
    super.dispose();
  }

  void _loadSettings() async {
    setState(() => _isLoading = true);
    
    try {
      final appState = context.read<AppStateProvider>();
      
      // Load doctor name
      final doctorName = await StorageService.instance.getSetting('doctor_name');
      if (doctorName != null) {
        _doctorNameController.text = doctorName;
        appState.setDoctorName(doctorName);
      }

      // Load API key
      final apiKey = await StorageService.instance.getSecureData('nvidia_api_key');
      if (apiKey != null) {
        _apiKeyController.text = apiKey;
        appState.setApiKey(apiKey);
      }

      // Load language preference
      final language = await StorageService.instance.getSetting('selected_language');
      if (language != null) {
        appState.setSelectedLanguage(language);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to load settings: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppStateProvider>(
      builder: (context, appState, child) {
        return AppScaffold(
          title: 'Settings',
          onBackPressed: () => appState.navigateToHome(),
          body: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _buildBody(context, appState),
        );
      },
    );
  }

  Widget _buildBody(BuildContext context, AppStateProvider appState) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Doctor Information Section
          _buildSectionCard(
            title: 'Doctor Information',
            icon: Icons.person,
            children: [
              _buildTextField(
                controller: _doctorNameController,
                label: 'Doctor Name',
                hint: 'Enter your full name',
                icon: Icons.badge,
                onChanged: (value) => appState.setDoctorName(value.isEmpty ? null : value),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // API Configuration Section
          _buildSectionCard(
            title: 'NVIDIA Riva Configuration',
            icon: Icons.api,
            children: [
              _buildTextField(
                controller: _apiKeyController,
                label: 'API Key',
                hint: 'Enter your NVIDIA Riva API key',
                icon: Icons.key,
                obscureText: true,
                onChanged: (value) => appState.setApiKey(value.isEmpty ? null : value),
              ),
              const SizedBox(height: 12),
              _buildApiKeyStatus(appState),
              const SizedBox(height: 12),
              _buildApiKeyHelp(),
            ],
          ),

          const SizedBox(height: 24),

          // Language Settings Section
          _buildSectionCard(
            title: 'Language Settings',
            icon: Icons.language,
            children: [
              _buildLanguageDropdown(appState),
            ],
          ),

          const SizedBox(height: 24),

          // App Information Section
          _buildSectionCard(
            title: 'App Information',
            icon: Icons.info,
            children: [
              _buildInfoRow('Version', '1.0.0'),
              _buildInfoRow('Build', '1'),
              _buildInfoRow('Platform', 'Flutter'),
            ],
          ),

          const SizedBox(height: 32),

          // Save Button
          ElevatedButton(
            onPressed: () => _saveSettings(appState),
            child: const Text('Save Settings'),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool obscureText = false,
    Function(String)? onChanged,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      onChanged: onChanged,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon),
        border: const OutlineInputBorder(),
        filled: true,
        fillColor: Colors.grey[50],
        suffixIcon: obscureText
            ? IconButton(
                icon: Icon(
                  obscureText ? Icons.visibility : Icons.visibility_off,
                ),
                onPressed: () {
                  setState(() {
                    obscureText = !obscureText;
                  });
                },
              )
            : null,
      ),
    );
  }

  Widget _buildApiKeyStatus(AppStateProvider appState) {
    final hasValidKey = appState.hasValidApiKey;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: hasValidKey ? Colors.green[50] : Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: hasValidKey ? Colors.green[200]! : Colors.red[200]!,
        ),
      ),
      child: Row(
        children: [
          Icon(
            hasValidKey ? Icons.check_circle : Icons.error,
            color: hasValidKey ? Colors.green[600] : Colors.red[600],
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            hasValidKey ? 'API key is valid' : 'API key is required',
            style: TextStyle(
              color: hasValidKey ? Colors.green[700] : Colors.red[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildApiKeyHelp() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.help,
                color: Colors.blue[600],
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'How to get your API key:',
                style: TextStyle(
                  color: Colors.blue[700],
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '1. Visit NVIDIA NGC (catalog.ngc.nvidia.com)\n'
            '2. Sign up or log in to your account\n'
            '3. Navigate to the Riva ASR service\n'
            '4. Generate an API key for Canary-1B ASR',
            style: TextStyle(
              color: Colors.blue[600],
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageDropdown(AppStateProvider appState) {
    return DropdownButtonFormField<String>(
      value: appState.selectedLanguage,
      decoration: const InputDecoration(
        labelText: 'Transcription Language',
        prefixIcon: Icon(Icons.translate),
        border: OutlineInputBorder(),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      items: appState.supportedLanguages.entries.map((entry) {
        return DropdownMenuItem(
          value: entry.key,
          child: Text(entry.value),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          appState.setSelectedLanguage(value);
        }
      },
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _saveSettings(AppStateProvider appState) async {
    setState(() => _isLoading = true);

    try {
      // Save doctor name
      if (_doctorNameController.text.isNotEmpty) {
        await StorageService.instance.saveSetting(
          'doctor_name',
          _doctorNameController.text,
        );
      }

      // Save API key securely
      if (_apiKeyController.text.isNotEmpty) {
        await StorageService.instance.saveSecureData(
          'nvidia_api_key',
          _apiKeyController.text,
        );
      }

      // Save language preference
      await StorageService.instance.saveSetting(
        'selected_language',
        appState.selectedLanguage,
      );

      _showSuccessSnackBar('Settings saved successfully');
    } catch (e) {
      _showErrorSnackBar('Failed to save settings: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
