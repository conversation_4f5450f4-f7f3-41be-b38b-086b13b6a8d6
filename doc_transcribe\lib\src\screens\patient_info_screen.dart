import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/app_state_provider.dart';
import '../providers/patient_provider.dart';
import '../models/consultation.dart';
import '../utils/uuid_generator.dart';
import '../widgets/app_scaffold.dart';

class PatientInfoScreen extends StatefulWidget {
  const PatientInfoScreen({super.key});

  @override
  State<PatientInfoScreen> createState() => _PatientInfoScreenState();
}

class _PatientInfoScreenState extends State<PatientInfoScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ageController = TextEditingController();
  final _mrnController = TextEditingController();
  final _reasonController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    final patientProvider = context.read<PatientProvider>();
    _nameController.text = patientProvider.name;
    _ageController.text = patientProvider.age?.toString() ?? '';
    _mrnController.text = patientProvider.medicalRecordNumber;
    _reasonController.text = patientProvider.reasonForVisit;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ageController.dispose();
    _mrnController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<AppStateProvider, PatientProvider>(
      builder: (context, appState, patientProvider, child) {
        return AppScaffold(
          title: 'Patient Information',
          onBackPressed: () => appState.navigateToHome(),
          body: _buildBody(context, appState, patientProvider),
        );
      },
    );
  }

  Widget _buildBody(BuildContext context, AppStateProvider appState, PatientProvider patientProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.person_add,
                          size: 28,
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Enter Patient Details',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Please fill in the patient information before starting the consultation.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Required fields section
            _buildSectionHeader('Required Information'),
            const SizedBox(height: 16),

            _buildTextField(
              controller: _nameController,
              label: 'Patient Name',
              hint: 'Enter full name',
              icon: Icons.person,
              isRequired: true,
              onChanged: patientProvider.setName,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Patient name is required';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: _buildTextField(
                    controller: _ageController,
                    label: 'Age',
                    hint: 'Years',
                    icon: Icons.cake,
                    isRequired: true,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(3),
                    ],
                    onChanged: (value) {
                      final age = int.tryParse(value);
                      patientProvider.setAge(age);
                    },
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Age is required';
                      }
                      final age = int.tryParse(value);
                      if (age == null || age <= 0 || age > 150) {
                        return 'Enter valid age';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 3,
                  child: _buildGenderDropdown(patientProvider),
                ),
              ],
            ),

            const SizedBox(height: 32),

            // Optional fields section
            _buildSectionHeader('Optional Information'),
            const SizedBox(height: 16),

            _buildTextField(
              controller: _mrnController,
              label: 'Medical Record Number',
              hint: 'Enter MRN (optional)',
              icon: Icons.badge,
              onChanged: patientProvider.setMedicalRecordNumber,
            ),

            const SizedBox(height: 16),

            _buildTextField(
              controller: _reasonController,
              label: 'Reason for Visit',
              hint: 'Brief description of the visit purpose (optional)',
              icon: Icons.description,
              maxLines: 3,
              onChanged: patientProvider.setReasonForVisit,
            ),

            const SizedBox(height: 32),

            // Form summary
            if (patientProvider.isFormValid) ...[
              _buildFormSummary(patientProvider),
              const SizedBox(height: 24),
            ],

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _clearForm(patientProvider),
                    child: const Text('Clear Form'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: patientProvider.isFormValid
                        ? () => _proceedToRecording(context, appState, patientProvider)
                        : null,
                    child: const Text('Start Recording'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
        color: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool isRequired = false,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    int maxLines = 1,
    Function(String)? onChanged,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      maxLines: maxLines,
      onChanged: onChanged,
      validator: validator,
      decoration: InputDecoration(
        labelText: isRequired ? '$label *' : label,
        hintText: hint,
        prefixIcon: Icon(icon),
        border: const OutlineInputBorder(),
        filled: true,
        fillColor: Colors.grey[50],
      ),
    );
  }

  Widget _buildGenderDropdown(PatientProvider patientProvider) {
    return DropdownButtonFormField<String>(
      value: patientProvider.gender,
      decoration: const InputDecoration(
        labelText: 'Gender *',
        prefixIcon: Icon(Icons.wc),
        border: OutlineInputBorder(),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      items: patientProvider.genderOptions.map((gender) {
        return DropdownMenuItem(
          value: gender,
          child: Text(gender),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          patientProvider.setGender(value);
        }
      },
    );
  }

  Widget _buildFormSummary(PatientProvider patientProvider) {
    return Card(
      color: Colors.green[50],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.green[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Patient Information Summary',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '${patientProvider.name}, ${patientProvider.age} years old, ${patientProvider.gender}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (patientProvider.medicalRecordNumber.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                'MRN: ${patientProvider.medicalRecordNumber}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
            if (patientProvider.reasonForVisit.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                'Reason: ${patientProvider.reasonForVisit}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _clearForm(PatientProvider patientProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Form'),
        content: const Text('Are you sure you want to clear all patient information?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              patientProvider.clearForm();
              _nameController.clear();
              _ageController.clear();
              _mrnController.clear();
              _reasonController.clear();
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _proceedToRecording(
    BuildContext context,
    AppStateProvider appState,
    PatientProvider patientProvider,
  ) {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      // Create patient from form data
      final patient = patientProvider.createPatient();

      // Create new consultation
      final consultation = Consultation(
        id: UuidGenerator.generate(),
        patient: patient,
        status: ConsultationStatus.draft,
        doctorName: appState.doctorName,
      );

      // Navigate to recording screen
      appState.navigateToRecording(consultation);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error creating consultation: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
