import 'dart:io';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:share_plus/share_plus.dart';
import '../models/consultation.dart';
import '../models/transcript_segment.dart';
import '../utils/constants.dart';

class PdfService {
  static final PdfService _instance = PdfService._internal();
  static PdfService get instance => _instance;
  PdfService._internal();

  /// Generate PDF report for a consultation
  Future<String> generateConsultationReport(
    Consultation consultation, {
    String? additionalNotes,
  }) async {
    try {
      final pdf = pw.Document();

      // Add cover page
      pdf.addPage(_buildCoverPage(consultation));

      // Add transcript pages
      if (consultation.transcript.isNotEmpty) {
        pdf.addPage(_buildTranscriptPage(consultation.transcript));
      }

      // Add notes page if available
      if (additionalNotes != null && additionalNotes.isNotEmpty) {
        pdf.addPage(_buildNotesPage(additionalNotes));
      }

      // Save PDF to device
      final pdfPath = await _savePdfToDevice(pdf, consultation);
      return pdfPath;
    } catch (e) {
      throw PdfException('Failed to generate PDF: $e');
    }
  }

  /// Build cover page
  pw.Page _buildCoverPage(Consultation consultation) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Header
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                color: PdfColors.blue50,
                border: pw.Border.all(color: PdfColors.blue200),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                children: [
                  pw.Text(
                    'Medical Consultation Report',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.blue800,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'DocScribe - AI-Powered Medical Transcription',
                    style: const pw.TextStyle(
                      fontSize: 12,
                      color: PdfColors.blue600,
                    ),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 30),

            // Patient Information
            _buildInfoSection(
              'Patient Information',
              [
                ['Name:', consultation.patient.name],
                ['Age:', '${consultation.patient.age} years'],
                ['Gender:', consultation.patient.gender],
                if (consultation.patient.patientId != null)
                  ['Patient ID:', consultation.patient.patientId!],
                if (consultation.patient.reasonForVisit != null)
                  ['Reason for Visit:', consultation.patient.reasonForVisit!],
              ],
            ),

            pw.SizedBox(height: 20),

            // Consultation Information
            _buildInfoSection(
              'Consultation Details',
              [
                ['Date:', _formatDate(consultation.createdAt)],
                ['Time:', _formatTime(consultation.createdAt)],
                ['Doctor:', consultation.doctorName ?? 'Not specified'],
                if (consultation.duration != null)
                  ['Duration:', _formatDuration(consultation.duration!)],
                ['Status:', consultation.status.name.toUpperCase()],
              ],
            ),

            pw.SizedBox(height: 20),

            // Statistics
            if (consultation.transcript.isNotEmpty)
              _buildInfoSection(
                'Transcript Statistics',
                [
                  ['Total Segments:', '${consultation.transcript.length}'],
                  ['Total Words:', '${consultation.wordCount}'],
                  ['Doctor Words:', '${_countDoctorWords(consultation)}'],
                  ['Patient Words:', '${_countPatientWords(consultation)}'],
                ],
              ),

            pw.Spacer(),

            // Footer
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300),
              ),
              child: pw.Text(
                'This report contains confidential patient information. '
                'Unauthorized dissemination is strictly prohibited.',
                style: const pw.TextStyle(
                  fontSize: 8,
                  color: PdfColors.grey600,
                ),
                textAlign: pw.TextAlign.center,
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build transcript page
  pw.Page _buildTranscriptPage(List<TranscriptSegment> transcript) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Header
            pw.Text(
              'Consultation Transcript',
              style: pw.TextStyle(
                fontSize: 18,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.blue800,
              ),
            ),

            pw.SizedBox(height: 20),

            // Transcript content
            pw.Expanded(
              child: pw.ListView.builder(
                itemCount: transcript.length,
                itemBuilder: (context, index) {
                  final segment = transcript[index];
                  return pw.Container(
                    margin: const pw.EdgeInsets.only(bottom: 15),
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        // Speaker and timestamp
                        pw.Row(
                          children: [
                            pw.Container(
                              padding: const pw.EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: pw.BoxDecoration(
                                color: segment.speaker == SpeakerType.doctor
                                    ? PdfColors.blue100
                                    : PdfColors.green100,
                                borderRadius: pw.BorderRadius.circular(4),
                              ),
                              child: pw.Text(
                                segment.speakerLabel,
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  fontWeight: pw.FontWeight.bold,
                                  color: segment.speaker == SpeakerType.doctor
                                      ? PdfColors.blue800
                                      : PdfColors.green800,
                                ),
                              ),
                            ),
                            pw.SizedBox(width: 10),
                            pw.Text(
                              segment.formattedTimestamp,
                              style: const pw.TextStyle(
                                fontSize: 9,
                                color: PdfColors.grey600,
                              ),
                            ),
                          ],
                        ),

                        pw.SizedBox(height: 5),

                        // Transcript text
                        pw.Text(
                          segment.text,
                          style: const pw.TextStyle(
                            fontSize: 11,
                            lineSpacing: 1.4,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build notes page
  pw.Page _buildNotesPage(String notes) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Header
            pw.Text(
              'Doctor\'s Notes & Impression',
              style: pw.TextStyle(
                fontSize: 18,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.blue800,
              ),
            ),

            pw.SizedBox(height: 20),

            // Notes content
            pw.Expanded(
              child: pw.Text(
                notes,
                style: const pw.TextStyle(
                  fontSize: 12,
                  lineSpacing: 1.5,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build information section
  pw.Widget _buildInfoSection(String title, List<List<String>> data) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue800,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          children: data.map((row) {
            return pw.TableRow(
              children: [
                pw.Container(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(
                    row[0],
                    style: pw.TextStyle(
                      fontSize: 10,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ),
                pw.Container(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(
                    row[1],
                    style: const pw.TextStyle(fontSize: 10),
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Save PDF to device storage
  Future<String> _savePdfToDevice(pw.Document pdf, Consultation consultation) async {
    final directory = await getApplicationDocumentsDirectory();
    final docScribeDir = Directory(path.join(directory.path, 'DocScribe', 'PDFs'));
    await docScribeDir.create(recursive: true);

    final fileName = 'consultation_${consultation.patient.name.replaceAll(' ', '_')}_${consultation.createdAt.millisecondsSinceEpoch}.pdf';
    final filePath = path.join(docScribeDir.path, fileName);

    final file = File(filePath);
    await file.writeAsBytes(await pdf.save());

    return filePath;
  }

  /// Share PDF file
  Future<void> sharePdf(String filePath) async {
    try {
      await Share.shareXFiles([XFile(filePath)]);
    } catch (e) {
      throw PdfException('Failed to share PDF: $e');
    }
  }

  /// Helper methods
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTime(DateTime date) {
    return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes}m ${seconds}s';
  }

  int _countDoctorWords(Consultation consultation) {
    return consultation.transcript
        .where((segment) => segment.speaker == SpeakerType.doctor)
        .map((segment) => segment.text.split(' ').length)
        .fold(0, (sum, count) => sum + count);
  }

  int _countPatientWords(Consultation consultation) {
    return consultation.transcript
        .where((segment) => segment.speaker == SpeakerType.patient)
        .map((segment) => segment.text.split(' ').length)
        .fold(0, (sum, count) => sum + count);
  }
}

class PdfException implements Exception {
  final String message;
  PdfException(this.message);

  @override
  String toString() => 'PdfException: $message';
}
