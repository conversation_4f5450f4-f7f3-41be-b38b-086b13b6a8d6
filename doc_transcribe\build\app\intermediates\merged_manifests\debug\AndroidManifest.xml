<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.doc_transcribe"
    android:versionCode="1"
    android:versionName="1.0.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="Manifest.permission.CAPTURE_AUDIO_OUTPUT" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <permission
        android:name="com.example.doc_transcribe.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.example.doc_transcribe.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="android.app.Application"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:icon="@mipmap/ic_launcher"
        android:label="DocTranscribe"
        android:requestLegacyExternalStorage="true" >
        <activity
            android:name="com.example.doc_transcribe.MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize" >

            <!--
                 Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI.
            -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />

            <intent-filter android:autoVerify="true" >
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!--
             Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
        -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <!-- File provider for sharing files -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.example.doc_transcribe.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
        <provider
            android:name="net.nfet.flutter.printing.PrintFileProvider"
            android:authorities="com.example.doc_transcribe.flutter.printing"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/flutter_printing_file_paths" />
        </provider>
        <!--
           Declares a provider which allows us to store files to share in
           '.../caches/share_plus' and grant the receiving action access
        -->
        <provider
            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
            android:authorities="com.example.doc_transcribe.flutter.share_provider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/flutter_share_file_paths" />
        </provider>
        <!--
           This manifest declared broadcast receiver allows us to use an explicit
           Intent when creating a PendingItent to be informed of the user's choice
        -->
        <receiver
            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
            android:exported="false" >
            <intent-filter>
                <action android:name="EXTRA_CHOSEN_COMPONENT" />
            </intent-filter>
        </receiver>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.example.doc_transcribe.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>